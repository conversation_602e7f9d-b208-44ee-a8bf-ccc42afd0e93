namespace DarkERP.Domain.Enums;

/// <summary>
/// Customer types - because not all customers are created equal
/// </summary>
public enum CustomerType
{
    Individual = 1,  // Regular people with regular problems
    Business = 2,    // Companies with complicated problems
    Government = 3,  // Bureaucracy with impossible problems
    NonProfit = 4    // Good intentions with no money
}

/// <summary>
/// Customer status - the relationship status with our customers
/// </summary>
public enum CustomerStatus
{
    Prospect = 0,    // Might buy something someday
    Active = 1,      // Currently buying things
    Inactive = 2,    // Used to buy things
    Blocked = 3      // We don't want their money anymore
}
