using AutoMapper;
using DarkERP.Application.DTOs;
using DarkERP.Domain.Entities;

namespace DarkERP.Application.Common.Mappings;

/// <summary>
/// AutoMapper profile - because manual mapping is for masochists
/// </summary>
public class MappingProfile : Profile
{
    public MappingProfile()
    {
        // Product mappings - the bread and butter
        CreateMap<Product, ProductDto>()
            .ForMember(dest => dest.IsLowStock, opt => opt.Ignore()) // Calculated in service
            .ForMember(dest => dest.ProfitMargin, opt => opt.Ignore()); // Calculated in service
            
        CreateMap<Product, ProductListDto>()
            .ForMember(dest => dest.IsLowStock, opt => opt.Ignore()); // Calculated in service
            
        CreateMap<CreateProductDto, Product>()
            .ForMember(dest => dest.Id, opt => opt.Ignore()) // Let the system generate it
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore()) // Set by base entity
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
            .ForMember(dest => dest.PurchaseOrderItems, opt => opt.Ignore())
            .ForMember(dest => dest.SalesOrderItems, opt => opt.Ignore());
            
        CreateMap<UpdateProductDto, Product>()
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore()) // Don't overwrite creation date
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore()) // Set in service
            .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.IsDeleted, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
            .ForMember(dest => dest.PurchaseOrderItems, opt => opt.Ignore())
            .ForMember(dest => dest.SalesOrderItems, opt => opt.Ignore());
    }
}
