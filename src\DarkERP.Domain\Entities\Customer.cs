using DarkERP.Domain.Common;
using DarkERP.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace DarkERP.Domain.Entities;

/// <summary>
/// Customer entity - the people who pay our bills (hopefully)
/// </summary>
public class Customer : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string? CompanyName { get; set; }
    
    [Required]
    [EmailAddress]
    [MaxLength(100)]
    public string Email { get; set; } = string.Empty;
    
    [MaxLength(20)]
    public string? Phone { get; set; }
    
    [MaxLength(200)]
    public string? Address { get; set; }
    
    [MaxLength(100)]
    public string? City { get; set; }
    
    [MaxLength(50)]
    public string? State { get; set; }
    
    [MaxLength(20)]
    public string? PostalCode { get; set; }
    
    [MaxLength(50)]
    public string? Country { get; set; } = "USA";
    
    public CustomerType Type { get; set; } = CustomerType.Individual;
    
    public CustomerStatus Status { get; set; } = CustomerStatus.Active;
    
    public decimal CreditLimit { get; set; } = 1000; // Default credit limit - optimistic, aren't we?
    
    public decimal CurrentBalance { get; set; } = 0;
    
    public DateTime? LastOrderDate { get; set; }
    
    // Navigation properties
    public virtual ICollection<SalesOrder> SalesOrders { get; set; } = new List<SalesOrder>();
    public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
    
    // Business logic
    public bool IsOverCreditLimit() => CurrentBalance > CreditLimit;
    
    public decimal AvailableCredit() => Math.Max(0, CreditLimit - CurrentBalance);
}
