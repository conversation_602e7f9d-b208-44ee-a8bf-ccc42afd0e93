using DarkERP.Domain.Common;
using DarkERP.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace DarkERP.Domain.Entities;

/// <summary>
/// Sales Order - where money hopefully flows in
/// </summary>
public class SalesOrder : BaseEntity
{
    [Required]
    [MaxLength(50)]
    public string OrderNumber { get; set; } = string.Empty;
    
    public DateTime OrderDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? RequiredDate { get; set; }
    
    public DateTime? ShippedDate { get; set; }
    
    public OrderStatus Status { get; set; } = OrderStatus.Pending;
    
    [Required]
    public Guid CustomerId { get; set; }
    
    public Guid? EmployeeId { get; set; } // Sales rep
    
    public decimal SubTotal { get; set; }
    
    public decimal TaxAmount { get; set; }
    
    public decimal ShippingAmount { get; set; }
    
    public decimal TotalAmount { get; set; }
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    [MaxLength(200)]
    public string? ShippingAddress { get; set; }
    
    // Navigation properties
    public virtual Customer Customer { get; set; } = null!;
    public virtual Employee? Employee { get; set; }
    public virtual ICollection<SalesOrderItem> Items { get; set; } = new List<SalesOrderItem>();
    public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
    
    // Business logic
    public void CalculateTotals()
    {
        SubTotal = Items.Sum(i => i.LineTotal);
        TotalAmount = SubTotal + TaxAmount + ShippingAmount;
        UpdatedAt = DateTime.UtcNow;
    }
    
    public bool CanBeShipped() => Status == OrderStatus.Confirmed && Items.All(i => i.Product.StockQuantity >= i.Quantity);
    
    public void Ship(DateTime shippedDate)
    {
        if (!CanBeShipped())
            throw new InvalidOperationException("Order cannot be shipped - check status and inventory");
            
        ShippedDate = shippedDate;
        Status = OrderStatus.Shipped;
        UpdatedAt = DateTime.UtcNow;
    }
}

/// <summary>
/// Sales Order Item - the individual line items that make up an order
/// </summary>
public class SalesOrderItem : BaseEntity
{
    [Required]
    public Guid SalesOrderId { get; set; }
    
    [Required]
    public Guid ProductId { get; set; }
    
    [Required]
    public int Quantity { get; set; }
    
    [Required]
    public decimal UnitPrice { get; set; }
    
    public decimal Discount { get; set; } = 0;
    
    public decimal LineTotal => (UnitPrice * Quantity) - Discount;
    
    // Navigation properties
    public virtual SalesOrder SalesOrder { get; set; } = null!;
    public virtual Product Product { get; set; } = null!;
}
