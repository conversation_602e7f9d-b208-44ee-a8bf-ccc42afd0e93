namespace DarkERP.Domain.Enums;

/// <summary>
/// Product categories - because everything needs a box to fit in
/// </summary>
public enum ProductCategory
{
    Electronics = 1,
    Clothing = 2,
    Books = 3,
    HomeAndGarden = 4,
    Sports = 5,
    Automotive = 6,
    Health = 7,
    Beauty = 8,
    Toys = 9,
    Food = 10,
    Other = 99 // The catch-all for when we give up categorizing
}

/// <summary>
/// Product status - the lifecycle of a product
/// </summary>
public enum ProductStatus
{
    Draft = 0,      // Still figuring out what this thing is
    Active = 1,     // Ready to make money
    Inactive = 2,   // Taking a break
    Discontinued = 3 // Gone but not forgotten
}
