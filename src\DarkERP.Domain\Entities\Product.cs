using DarkERP.Domain.Common;
using DarkERP.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace DarkERP.Domain.Entities;

/// <summary>
/// Product entity - because someone has to sell something to keep the lights on
/// </summary>
public class Product : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string SKU { get; set; } = string.Empty; // Stock Keeping Unit, not to be confused with SKU-nk
    
    [Required]
    public decimal Price { get; set; }
    
    [Required]
    public decimal Cost { get; set; }
    
    public int StockQuantity { get; set; }
    
    public int ReorderLevel { get; set; } // When to panic and reorder
    
    public ProductCategory Category { get; set; }
    
    public ProductStatus Status { get; set; } = ProductStatus.Active;
    
    [MaxLength(200)]
    public string? Supplier { get; set; }
    
    [MaxLength(100)]
    public string? Brand { get; set; }
    
    public decimal Weight { get; set; }
    
    [MaxLength(50)]
    public string? WeightUnit { get; set; } = "kg";
    
    // Navigation properties - because relationships are complicated
    public virtual ICollection<PurchaseOrderItem> PurchaseOrderItems { get; set; } = new List<PurchaseOrderItem>();
    public virtual ICollection<SalesOrderItem> SalesOrderItems { get; set; } = new List<SalesOrderItem>();
    
    // Business logic - because even products have rules
    public bool IsLowStock() => StockQuantity <= ReorderLevel;
    
    public decimal GetProfitMargin() => Price > 0 ? ((Price - Cost) / Price) * 100 : 0;
    
    public void UpdateStock(int quantity, string reason = "Manual adjustment")
    {
        // Don't shoot the messenger if this goes negative
        StockQuantity += quantity;
        UpdatedAt = DateTime.UtcNow;
    }
}
