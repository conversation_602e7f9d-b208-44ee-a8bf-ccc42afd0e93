@using Microsoft.AspNetCore.Components.Authorization
@inject AuthenticationStateProvider AuthenticationStateProvider

<div class="sidebar">
    <div style="padding: 1rem 1.5rem; border-bottom: 1px solid var(--dark-border);">
        <h3 style="color: var(--dark-accent); margin: 0;">DarkERP</h3>
        <small style="color: var(--dark-text-muted);">Where Business Goes to Die</small>
    </div>
    
    <nav class="nav-menu">
        <NavLink class="nav-link" href="/" Match="NavLinkMatch.All">
            <span>🏠 Dashboard</span>
        </NavLink>
        
        <NavLink class="nav-link" href="/inventory">
            <span>📦 Inventory</span>
        </NavLink>
        
        <NavLink class="nav-link" href="/purchasing">
            <span>🛒 Purchasing</span>
        </NavLink>
        
        <NavLink class="nav-link" href="/sales">
            <span>💰 Sales</span>
        </NavLink>
        
        <NavLink class="nav-link" href="/finance">
            <span>💳 Finance</span>
        </NavLink>
        
        <NavLink class="nav-link" href="/hr">
            <span>👥 HR</span>
        </NavLink>
        
        <NavLink class="nav-link" href="/crm">
            <span>🤝 CRM</span>
        </NavLink>
        
        <div style="margin-top: 2rem; padding: 0 1.5rem; border-top: 1px solid var(--dark-border); padding-top: 1rem;">
            <AuthorizeView>
                <Authorized>
                    <div style="color: var(--dark-text-muted); font-size: 0.8rem; margin-bottom: 0.5rem;">
                        Logged in as: @context.User.Identity?.Name
                    </div>
                    <a href="/Account/Logout" class="nav-link" style="padding: 0.5rem 0; color: var(--dark-danger);">
                        🚪 Logout
                    </a>
                </Authorized>
                <NotAuthorized>
                    <a href="/Account/Login" class="nav-link" style="padding: 0.5rem 0;">
                        🔑 Login
                    </a>
                </NotAuthorized>
            </AuthorizeView>
        </div>
    </nav>
</div>

<style>
    .nav-link span {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
</style>
