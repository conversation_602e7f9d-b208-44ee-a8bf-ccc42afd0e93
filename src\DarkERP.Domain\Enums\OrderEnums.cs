namespace DarkERP.Domain.Enums;

/// <summary>
/// Order status - the journey from hope to reality
/// </summary>
public enum OrderStatus
{
    Draft = 0,       // Still thinking about it
    Pending = 1,     // Waiting for approval
    Confirmed = 2,   // It's happening
    Shipped = 3,     // Out the door
    Delivered = 4,   // Mission accomplished
    Cancelled = 5,   // Dreams crushed
    Received = 6     // For purchase orders - stuff arrived
}

/// <summary>
/// Invoice status - the lifecycle of getting paid
/// </summary>
public enum InvoiceStatus
{
    Draft = 0,          // Still working on it
    Sent = 1,           // Fired into the void
    PartiallyPaid = 2,  // Getting somewhere
    Paid = 3,           // Victory!
    Overdue = 4,        // The awkward phase
    Cancelled = 5       // Giving up
}
