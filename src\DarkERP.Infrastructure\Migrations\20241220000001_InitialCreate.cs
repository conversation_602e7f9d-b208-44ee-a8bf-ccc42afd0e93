using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DarkERP.Infrastructure.Migrations
{
    /// <summary>
    /// Initial migration - where it all begins (and hopefully doesn't end in tears)
    /// </summary>
    public partial class InitialCreate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create Products table - the foundation of our empire
            migrationBuilder.CreateTable(
                name: "Products",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    SKU = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Cost = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    StockQuantity = table.Column<int>(type: "int", nullable: false),
                    ReorderLevel = table.Column<int>(type: "int", nullable: false),
                    Category = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    Supplier = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Brand = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Weight = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    WeightUnit = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UpdatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Products", x => x.Id);
                });

            // Create unique index on SKU - because duplicates are the devil
            migrationBuilder.CreateIndex(
                name: "IX_Products_SKU",
                table: "Products",
                column: "SKU",
                unique: true);

            // Insert some sample data - because empty tables are depressing
            migrationBuilder.InsertData(
                table: "Products",
                columns: new[] { "Id", "Name", "Description", "SKU", "Price", "Cost", "StockQuantity", "ReorderLevel", "Category", "Status", "Supplier", "Brand", "Weight", "WeightUnit", "CreatedAt", "UpdatedAt", "CreatedBy", "UpdatedBy", "IsDeleted", "DeletedAt", "DeletedBy" },
                values: new object[,]
                {
                    { Guid.NewGuid(), "Widget of Doom", "A widget so powerful it might actually work", "WOD-001", 99.99m, 45.50m, 100, 10, 1, 1, "Widgets R Us", "DoomCorp", 2.5m, "kg", DateTime.UtcNow, null, "System", null, false, null, null },
                    { Guid.NewGuid(), "Gadget of Despair", "When hope is lost, this gadget remains", "GOD-002", 149.99m, 67.25m, 50, 5, 1, 1, "Gadget Galaxy", "DespairTech", 1.8m, "kg", DateTime.UtcNow, null, "System", null, false, null, null },
                    { Guid.NewGuid(), "Thingamajig of Chaos", "Chaos in a convenient package", "TOC-003", 199.99m, 89.99m, 25, 3, 1, 1, "Chaos Co.", "MayhemInc", 3.2m, "kg", DateTime.UtcNow, null, "System", null, false, null, null }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop the Products table - burning bridges like a pro
            migrationBuilder.DropTable(
                name: "Products");
        }
    }
}
