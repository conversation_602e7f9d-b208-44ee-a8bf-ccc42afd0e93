using DarkERP.Application.Common.Interfaces;
using DarkERP.Domain.Entities;

namespace DarkERP.Application.Services;

/// <summary>
/// Inventory service - keeping track of stuff so we don't run out
/// </summary>
public interface IInventoryService
{
    Task<IEnumerable<Product>> GetLowStockProductsAsync(CancellationToken cancellationToken = default);
    Task<decimal> GetTotalInventoryValueAsync(CancellationToken cancellationToken = default);
    Task AdjustStockAsync(Guid productId, int quantity, string reason, CancellationToken cancellationToken = default);
}

/// <summary>
/// Purchasing service - spending money wisely (hopefully)
/// </summary>
public interface IPurchasingService : IService<PurchaseOrder>
{
    Task<PurchaseOrder> CreatePurchaseOrderAsync(PurchaseOrder order, CancellationToken cancellationToken = default);
    Task<PurchaseOrder> ApprovePurchaseOrderAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<PurchaseOrder> ReceivePurchaseOrderAsync(Guid orderId, DateTime receivedDate, CancellationToken cancellationToken = default);
    Task<IEnumerable<PurchaseOrder>> GetPendingOrdersAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Sales service - making money (the whole point of this exercise)
/// </summary>
public interface ISalesService : IService<SalesOrder>
{
    Task<SalesOrder> CreateSalesOrderAsync(SalesOrder order, CancellationToken cancellationToken = default);
    Task<SalesOrder> ConfirmSalesOrderAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<SalesOrder> ShipSalesOrderAsync(Guid orderId, DateTime shippedDate, CancellationToken cancellationToken = default);
    Task<IEnumerable<SalesOrder>> GetOrdersByCustomerAsync(Guid customerId, CancellationToken cancellationToken = default);
    Task<decimal> GetSalesRevenueAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
}

/// <summary>
/// Finance service - counting beans and chasing payments
/// </summary>
public interface IFinanceService : IService<Invoice>
{
    Task<Invoice> CreateInvoiceAsync(Invoice invoice, CancellationToken cancellationToken = default);
    Task<Invoice> CreateInvoiceFromSalesOrderAsync(Guid salesOrderId, CancellationToken cancellationToken = default);
    Task<Invoice> RecordPaymentAsync(Guid invoiceId, decimal amount, DateTime paidDate, CancellationToken cancellationToken = default);
    Task<IEnumerable<Invoice>> GetOverdueInvoicesAsync(CancellationToken cancellationToken = default);
    Task<decimal> GetAccountsReceivableAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// HR service - managing the human resources (aka dealing with people problems)
/// </summary>
public interface IHRService : IService<Employee>
{
    Task<Employee> HireEmployeeAsync(Employee employee, CancellationToken cancellationToken = default);
    Task<Employee> TerminateEmployeeAsync(Guid employeeId, DateTime terminationDate, string reason, CancellationToken cancellationToken = default);
    Task<IEnumerable<Employee>> GetEmployeesByDepartmentAsync(string department, CancellationToken cancellationToken = default);
    Task<decimal> GetTotalPayrollAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// CRM service - keeping customers happy (or at least not completely pissed off)
/// </summary>
public interface ICRMService : IService<Customer>
{
    Task<Customer> CreateCustomerAsync(Customer customer, CancellationToken cancellationToken = default);
    Task<Customer> UpdateCreditLimitAsync(Guid customerId, decimal newLimit, CancellationToken cancellationToken = default);
    Task<IEnumerable<Customer>> GetCustomersOverCreditLimitAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Customer>> GetTopCustomersByRevenueAsync(int count, CancellationToken cancellationToken = default);
    Task<decimal> GetCustomerLifetimeValueAsync(Guid customerId, CancellationToken cancellationToken = default);
}
