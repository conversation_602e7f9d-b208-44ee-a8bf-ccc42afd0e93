<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
    <PackageReference Include="MediatR" Version="12.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DarkERP.Application\DarkERP.Application.csproj" />
    <ProjectReference Include="..\DarkERP.Infrastructure\DarkERP.Infrastructure.csproj" />
    <ProjectReference Include="..\DarkERP.Domain\DarkERP.Domain.csproj" />
  </ItemGroup>

</Project>
