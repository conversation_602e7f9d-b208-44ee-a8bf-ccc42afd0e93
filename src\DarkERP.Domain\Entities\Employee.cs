using DarkERP.Domain.Common;
using DarkERP.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace DarkERP.Domain.Entities;

/// <summary>
/// Employee entity - the souls we've captured to do our bidding
/// </summary>
public class Employee : BaseEntity
{
    [Required]
    [MaxLength(50)]
    public string FirstName { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(50)]
    public string LastName { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    [MaxLength(100)]
    public string Email { get; set; } = string.Empty;
    
    [MaxLength(20)]
    public string? Phone { get; set; }
    
    [MaxLength(200)]
    public string? Address { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string Position { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string Department { get; set; } = string.Empty;
    
    public DateTime HireDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? TerminationDate { get; set; }
    
    public decimal Salary { get; set; }
    
    public EmployeeStatus Status { get; set; } = EmployeeStatus.Active;
    
    public Guid? ManagerId { get; set; }
    
    // Navigation properties
    public virtual Employee? Manager { get; set; }
    public virtual ICollection<Employee> Subordinates { get; set; } = new List<Employee>();
    public virtual ICollection<SalesOrder> SalesOrders { get; set; } = new List<SalesOrder>();
    public virtual ICollection<PurchaseOrder> PurchaseOrders { get; set; } = new List<PurchaseOrder>();
    
    // Computed properties
    public string FullName => $"{FirstName} {LastName}";
    
    public int YearsOfService => (int)((TerminationDate ?? DateTime.UtcNow) - HireDate).TotalDays / 365;
    
    // Business logic
    public bool IsActive() => Status == EmployeeStatus.Active && TerminationDate == null;
    
    public void Terminate(DateTime terminationDate, string reason = "It's not you, it's us... actually, it's you")
    {
        TerminationDate = terminationDate;
        Status = EmployeeStatus.Terminated;
        UpdatedAt = DateTime.UtcNow;
    }
}
