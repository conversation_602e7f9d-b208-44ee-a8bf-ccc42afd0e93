namespace DarkERP.Domain.Exceptions;

/// <summary>
/// Base domain exception - when business rules get violated
/// </summary>
public abstract class DomainException : Exception
{
    protected DomainException(string message) : base(message) { }
    
    protected DomainException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// Thrown when someone tries to do something they shouldn't
/// </summary>
public class BusinessRuleViolationException : DomainException
{
    public BusinessRuleViolationException(string rule, string details = "") 
        : base($"Business rule violated: {rule}. {details}")
    {
        Rule = rule;
        Details = details;
    }
    
    public string Rule { get; }
    public string Details { get; }
}

/// <summary>
/// Thrown when inventory goes negative - because we can't sell what we don't have
/// </summary>
public class InsufficientInventoryException : DomainException
{
    public InsufficientInventoryException(string productName, int requested, int available)
        : base($"Insufficient inventory for {productName}. Requested: {requested}, Available: {available}")
    {
        ProductName = productName;
        RequestedQuantity = requested;
        AvailableQuantity = available;
    }
    
    public string ProductName { get; }
    public int RequestedQuantity { get; }
    public int AvailableQuantity { get; }
}

/// <summary>
/// Thrown when customers exceed their credit limit - because money doesn't grow on trees
/// </summary>
public class CreditLimitExceededException : DomainException
{
    public CreditLimitExceededException(string customerName, decimal currentBalance, decimal creditLimit, decimal attemptedAmount)
        : base($"Credit limit exceeded for {customerName}. Current: {currentBalance:C}, Limit: {creditLimit:C}, Attempted: {attemptedAmount:C}")
    {
        CustomerName = customerName;
        CurrentBalance = currentBalance;
        CreditLimit = creditLimit;
        AttemptedAmount = attemptedAmount;
    }
    
    public string CustomerName { get; }
    public decimal CurrentBalance { get; }
    public decimal CreditLimit { get; }
    public decimal AttemptedAmount { get; }
}
