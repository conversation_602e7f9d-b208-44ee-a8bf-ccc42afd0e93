using DarkERP.Domain.Entities;

namespace DarkERP.Application.Common.Interfaces;

/// <summary>
/// Unit of Work pattern - because transactions are important
/// </summary>
public interface IUnitOfWork : IDisposable
{
    // Repository properties - one-stop shopping for data access
    IRepository<Product> Products { get; }
    IRepository<Customer> Customers { get; }
    IRepository<Employee> Employees { get; }
    IRepository<SalesOrder> SalesOrders { get; }
    IRepository<SalesOrderItem> SalesOrderItems { get; }
    IRepository<PurchaseOrder> PurchaseOrders { get; }
    IRepository<PurchaseOrderItem> PurchaseOrderItems { get; }
    IRepository<Invoice> Invoices { get; }
    IRepository<InvoiceItem> InvoiceItems { get; }
    
    // Transaction management - because consistency matters
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
}
