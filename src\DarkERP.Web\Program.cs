using DarkERP.Application.Common.Interfaces;
using DarkERP.Application.Common.Mappings;
using DarkERP.Application.Services;
using DarkERP.Infrastructure.Data;
using DarkERP.Infrastructure.Repositories;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container - because dependency injection is life
builder.Services.AddRazorPages();
builder.Services.AddServerSideBlazor();

// Database configuration - where our data lives (hopefully safely)
builder.Services.AddDbContext<AppDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Identity configuration - because we need to know who's messing with our data
builder.Services.AddDefaultIdentity<ApplicationUser>(options =>
{
    // Password requirements - because security matters (sort of)
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireUppercase = true;
    options.Password.RequiredLength = 6;
    options.Password.RequiredUniqueChars = 1;
    
    // Lockout settings - because we don't want brute force attacks
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
    options.Lockout.MaxFailedAccessAttempts = 5;
    options.Lockout.AllowedForNewUsers = true;
    
    // User settings
    options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
    options.User.RequireUniqueEmail = true;
})
.AddRoles<IdentityRole>()
.AddEntityFrameworkStores<AppDbContext>();

// Repository and Unit of Work registration
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
builder.Services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

// Service registration - where the business logic lives
builder.Services.AddScoped<IProductService, ProductService>();

// AutoMapper configuration - because manual mapping is for masochists
builder.Services.AddAutoMapper(typeof(MappingProfile));

// MediatR configuration - for when we want to be fancy with CQRS
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(IProductService).Assembly));

var app = builder.Build();

// Configure the HTTP request pipeline - the journey of a request
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    app.UseHsts(); // Because HTTPS is not optional in 2024
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

// Authentication and Authorization - the bouncers of our app
app.UseAuthentication();
app.UseAuthorization();

app.MapRazorPages();
app.MapBlazorHub();
app.MapFallbackToPage("/_Host");

// Database initialization - because someone has to create the tables
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
    var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
    var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole>>();
    
    try
    {
        // Ensure database is created
        await context.Database.EnsureCreatedAsync();
        
        // Seed roles - because hierarchy matters
        await SeedRolesAsync(roleManager);
        
        // Seed admin user - because someone needs to be in charge
        await SeedAdminUserAsync(userManager);
    }
    catch (Exception ex)
    {
        // Log the exception here if you have logging
        Console.WriteLine($"Database initialization failed: {ex.Message}");
        // Don't crash the app, just log and continue
    }
}

app.Run();

// Helper methods for seeding data
static async Task SeedRolesAsync(RoleManager<IdentityRole> roleManager)
{
    string[] roles = { "Admin", "Manager", "Clerk" };
    
    foreach (var role in roles)
    {
        if (!await roleManager.RoleExistsAsync(role))
        {
            await roleManager.CreateAsync(new IdentityRole(role));
        }
    }
}

static async Task SeedAdminUserAsync(UserManager<ApplicationUser> userManager)
{
    const string adminEmail = "<EMAIL>";
    const string adminPassword = "DarkERP123!";
    
    var adminUser = await userManager.FindByEmailAsync(adminEmail);
    if (adminUser == null)
    {
        adminUser = new ApplicationUser
        {
            UserName = adminEmail,
            Email = adminEmail,
            FirstName = "Dark",
            LastName = "Administrator",
            EmailConfirmed = true,
            IsActive = true
        };
        
        var result = await userManager.CreateAsync(adminUser, adminPassword);
        if (result.Succeeded)
        {
            await userManager.AddToRoleAsync(adminUser, "Admin");
        }
    }
}
