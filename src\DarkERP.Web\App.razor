<Router AppAssembly="@typeof(App).Assembly">
    <Found Context="routeData">
        <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)">
            <NotAuthorized>
                @if (context.User.Identity?.IsAuthenticated != true)
                {
                    <RedirectToLogin />
                }
                else
                {
                    <div class="error">
                        <h3>Access Denied</h3>
                        <p>You don't have permission to access this resource. Try bribing the admin.</p>
                    </div>
                }
            </NotAuthorized>
        </AuthorizeRouteView>
        <FocusOnNavigate RouteData="@routeData" Selector="h1" />
    </Found>
    <NotFound>
        <PageTitle>Not found - DarkERP</PageTitle>
        <LayoutView Layout="@typeof(MainLayout)">
            <div class="error">
                <h1>404 - Page Not Found</h1>
                <p>The page you're looking for has vanished into the digital void. Much like our hopes and dreams.</p>
                <a href="/" class="btn btn-primary">Return to Dashboard</a>
            </div>
        </LayoutView>
    </NotFound>
</Router>
