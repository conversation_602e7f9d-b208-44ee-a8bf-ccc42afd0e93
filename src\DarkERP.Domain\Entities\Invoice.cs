using DarkERP.Domain.Common;
using DarkERP.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace DarkERP.Domain.Entities;

/// <summary>
/// Invoice - the piece of paper that hopefully turns into money
/// </summary>
public class Invoice : BaseEntity
{
    [Required]
    [MaxLength(50)]
    public string InvoiceNumber { get; set; } = string.Empty;
    
    public DateTime InvoiceDate { get; set; } = DateTime.UtcNow;
    
    public DateTime DueDate { get; set; } = DateTime.UtcNow.AddDays(30); // 30 days to pay, good luck collecting
    
    public DateTime? PaidDate { get; set; }
    
    public InvoiceStatus Status { get; set; } = InvoiceStatus.Draft;
    
    [Required]
    public Guid CustomerId { get; set; }
    
    public Guid? SalesOrderId { get; set; }
    
    public decimal SubTotal { get; set; }
    
    public decimal TaxAmount { get; set; }
    
    public decimal TotalAmount { get; set; }
    
    public decimal PaidAmount { get; set; } = 0;
    
    public decimal BalanceDue => TotalAmount - PaidAmount;
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    [MaxLength(50)]
    public string? PaymentTerms { get; set; } = "Net 30";
    
    // Navigation properties
    public virtual Customer Customer { get; set; } = null!;
    public virtual SalesOrder? SalesOrder { get; set; }
    public virtual ICollection<InvoiceItem> Items { get; set; } = new List<InvoiceItem>();
    
    // Business logic
    public void CalculateTotals()
    {
        SubTotal = Items.Sum(i => i.LineTotal);
        TotalAmount = SubTotal + TaxAmount;
        UpdatedAt = DateTime.UtcNow;
    }
    
    public bool IsOverdue() => Status != InvoiceStatus.Paid && DateTime.UtcNow > DueDate;
    
    public int DaysOverdue() => IsOverdue() ? (DateTime.UtcNow - DueDate).Days : 0;
    
    public void MarkAsPaid(decimal amount, DateTime paidDate)
    {
        PaidAmount += amount;
        
        if (PaidAmount >= TotalAmount)
        {
            Status = InvoiceStatus.Paid;
            PaidDate = paidDate;
        }
        else
        {
            Status = InvoiceStatus.PartiallyPaid;
        }
        
        UpdatedAt = DateTime.UtcNow;
    }
}

/// <summary>
/// Invoice Item - the individual charges on an invoice
/// </summary>
public class InvoiceItem : BaseEntity
{
    [Required]
    public Guid InvoiceId { get; set; }
    
    [Required]
    [MaxLength(200)]
    public string Description { get; set; } = string.Empty;
    
    [Required]
    public int Quantity { get; set; }
    
    [Required]
    public decimal UnitPrice { get; set; }
    
    public decimal LineTotal => UnitPrice * Quantity;
    
    // Navigation properties
    public virtual Invoice Invoice { get; set; } = null!;
}
