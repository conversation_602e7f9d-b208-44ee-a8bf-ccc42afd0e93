using DarkERP.Domain.Common;
using DarkERP.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace DarkERP.Domain.Entities;

/// <summary>
/// Purchase Order - where money flows out (hopefully for good reasons)
/// </summary>
public class PurchaseOrder : BaseEntity
{
    [Required]
    [MaxLength(50)]
    public string OrderNumber { get; set; } = string.Empty;
    
    public DateTime OrderDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? RequiredDate { get; set; }
    
    public DateTime? ReceivedDate { get; set; }
    
    public OrderStatus Status { get; set; } = OrderStatus.Pending;
    
    [Required]
    [MaxLength(200)]
    public string SupplierName { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string? SupplierEmail { get; set; }
    
    [MaxLength(20)]
    public string? SupplierPhone { get; set; }
    
    public Guid? EmployeeId { get; set; } // Purchasing agent
    
    public decimal SubTotal { get; set; }
    
    public decimal TaxAmount { get; set; }
    
    public decimal ShippingAmount { get; set; }
    
    public decimal TotalAmount { get; set; }
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    // Navigation properties
    public virtual Employee? Employee { get; set; }
    public virtual ICollection<PurchaseOrderItem> Items { get; set; } = new List<PurchaseOrderItem>();
    
    // Business logic
    public void CalculateTotals()
    {
        SubTotal = Items.Sum(i => i.LineTotal);
        TotalAmount = SubTotal + TaxAmount + ShippingAmount;
        UpdatedAt = DateTime.UtcNow;
    }
    
    public void Receive(DateTime receivedDate)
    {
        if (Status != OrderStatus.Confirmed)
            throw new InvalidOperationException("Only confirmed orders can be received");
            
        ReceivedDate = receivedDate;
        Status = OrderStatus.Received;
        
        // Update product stock - because inventory management is a thing
        foreach (var item in Items)
        {
            item.Product.UpdateStock(item.Quantity, $"Received from PO {OrderNumber}");
        }
        
        UpdatedAt = DateTime.UtcNow;
    }
}

/// <summary>
/// Purchase Order Item - individual items we're buying
/// </summary>
public class PurchaseOrderItem : BaseEntity
{
    [Required]
    public Guid PurchaseOrderId { get; set; }
    
    [Required]
    public Guid ProductId { get; set; }
    
    [Required]
    public int Quantity { get; set; }
    
    [Required]
    public decimal UnitCost { get; set; }
    
    public decimal LineTotal => UnitCost * Quantity;
    
    // Navigation properties
    public virtual PurchaseOrder PurchaseOrder { get; set; } = null!;
    public virtual Product Product { get; set; } = null!;
}
