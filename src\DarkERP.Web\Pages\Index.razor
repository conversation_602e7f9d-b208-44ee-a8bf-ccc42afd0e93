@page "/"
@using DarkERP.Application.Services
@inject IProductService ProductService
@attribute [Authorize]

<PageTitle>Dashboard - DarkERP</PageTitle>

<div class="dashboard-grid">
    <div class="widget">
        <div class="widget-title">Total Products</div>
        <div class="widget-value">@totalProducts</div>
        <div class="widget-subtitle">Items in inventory</div>
    </div>
    
    <div class="widget">
        <div class="widget-title">Low Stock Items</div>
        <div class="widget-value" style="color: var(--dark-warning);">@lowStockCount</div>
        <div class="widget-subtitle">Need immediate attention</div>
    </div>
    
    <div class="widget">
        <div class="widget-title">Total Inventory Value</div>
        <div class="widget-value">$@inventoryValue.ToString("N2")</div>
        <div class="widget-subtitle">Current stock value</div>
    </div>
    
    <div class="widget">
        <div class="widget-title">System Status</div>
        <div class="widget-value" style="color: var(--dark-success);">Operational</div>
        <div class="widget-subtitle">All systems functioning (barely)</div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 style="margin: 0;">Recent Activity</h5>
    </div>
    <div class="card-body">
        <div style="color: var(--dark-text-muted); text-align: center; padding: 2rem;">
            <p>📊 Welcome to DarkERP Dashboard</p>
            <p>Your one-stop shop for business despair and inventory nightmares.</p>
            <p>Navigate using the sidebar to explore different modules.</p>
            <div style="margin-top: 1.5rem;">
                <a href="/inventory" class="btn btn-primary" style="margin-right: 1rem;">Manage Inventory</a>
                <a href="/sales" class="btn btn-secondary">View Sales</a>
            </div>
        </div>
    </div>
</div>

@if (lowStockProducts.Any())
{
    <div class="card">
        <div class="card-header">
            <h5 style="margin: 0; color: var(--dark-warning);">⚠️ Low Stock Alert</h5>
        </div>
        <div class="card-body">
            <table class="table">
                <thead>
                    <tr>
                        <th>Product</th>
                        <th>SKU</th>
                        <th>Current Stock</th>
                        <th>Reorder Level</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var product in lowStockProducts.Take(5))
                    {
                        <tr>
                            <td>@product.Name</td>
                            <td>@product.SKU</td>
                            <td>@product.StockQuantity</td>
                            <td>@product.ReorderLevel</td>
                            <td>
                                <span class="badge badge-warning">Low Stock</span>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
            @if (lowStockProducts.Count() > 5)
            {
                <div style="text-align: center; margin-top: 1rem;">
                    <a href="/inventory" class="btn btn-secondary">View All Low Stock Items</a>
                </div>
            }
        </div>
    </div>
}

@code {
    private int totalProducts = 0;
    private int lowStockCount = 0;
    private decimal inventoryValue = 0;
    private IEnumerable<DarkERP.Application.DTOs.ProductListDto> lowStockProducts = new List<DarkERP.Application.DTOs.ProductListDto>();

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Load dashboard data
            var allProducts = await ProductService.GetProductListAsync();
            totalProducts = allProducts.Count();
            
            lowStockProducts = await ProductService.GetLowStockProductsAsync();
            lowStockCount = lowStockProducts.Count();
            
            // Calculate inventory value (simplified - using price instead of cost for demo)
            inventoryValue = allProducts.Sum(p => p.Price * p.StockQuantity);
        }
        catch (Exception ex)
        {
            // In a real app, you'd log this properly
            Console.WriteLine($"Error loading dashboard data: {ex.Message}");
        }
    }
}
