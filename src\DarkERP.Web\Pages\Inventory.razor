@page "/inventory"
@using DarkERP.Application.DTOs
@using DarkERP.Application.Services
@using DarkERP.Domain.Enums
@inject IProductService ProductService
@inject IJSRuntime JSRuntime
@attribute [Authorize]

<PageTitle>Inventory Management - DarkERP</PageTitle>

<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
    <div>
        <h2 style="margin: 0; color: var(--dark-text-primary);">Inventory Management</h2>
        <p style="color: var(--dark-text-muted); margin: 0.5rem 0 0 0;">Manage your products and stock levels</p>
    </div>
    <button class="btn btn-primary" @onclick="ShowCreateModal">
        ➕ Add New Product
    </button>
</div>

@if (isLoading)
{
    <div class="loading">
        <p>Loading products... (or they've all escaped)</p>
    </div>
}
else if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="error">
        <strong>Error:</strong> @errorMessage
    </div>
}
else
{
    <div class="card">
        <div class="card-header">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <h5 style="margin: 0;">Products (@products.Count())</h5>
                <div style="display: flex; gap: 1rem; align-items: center;">
                    <input type="text" @bind="searchTerm" @oninput="OnSearchChanged" 
                           placeholder="Search products..." class="form-control" style="width: 250px;" />
                    <button class="btn btn-secondary" @onclick="LoadProducts">🔄 Refresh</button>
                </div>
            </div>
        </div>
        <div class="card-body" style="padding: 0;">
            @if (products.Any())
            {
                <table class="table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>SKU</th>
                            <th>Price</th>
                            <th>Stock</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var product in filteredProducts)
                        {
                            <tr>
                                <td>
                                    <strong>@product.Name</strong>
                                </td>
                                <td>@product.SKU</td>
                                <td>$@product.Price.ToString("N2")</td>
                                <td>
                                    <span class="@(product.IsLowStock ? "badge badge-warning" : "")">
                                        @product.StockQuantity
                                        @if (product.IsLowStock)
                                        {
                                            <text> ⚠️</text>
                                        }
                                    </span>
                                </td>
                                <td>
                                    <span class="badge @GetStatusBadgeClass(product.Status)">
                                        @product.Status
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-secondary" style="margin-right: 0.5rem;" 
                                            @onclick="() => ShowEditModal(product.Id)">
                                        ✏️ Edit
                                    </button>
                                    <button class="btn btn-danger" 
                                            @onclick="() => DeleteProduct(product.Id)">
                                        🗑️ Delete
                                    </button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            }
            else
            {
                <div style="text-align: center; padding: 3rem; color: var(--dark-text-muted);">
                    <p>No products found. Time to add some inventory!</p>
                    <button class="btn btn-primary" @onclick="ShowCreateModal">
                        Add Your First Product
                    </button>
                </div>
            }
        </div>
    </div>
}

<!-- Create/Edit Modal -->
@if (showModal)
{
    <div class="modal-overlay" @onclick="HideModal">
        <div class="modal-content" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h4>@(isEditMode ? "Edit Product" : "Create New Product")</h4>
                <button class="btn-close" @onclick="HideModal">✕</button>
            </div>
            <div class="modal-body">
                <EditForm Model="productForm" OnValidSubmit="SaveProduct">
                    <DataAnnotationsValidator />
                    <ValidationSummary class="error" />
                    
                    <div class="form-group">
                        <label class="form-label">Name *</label>
                        <InputText @bind-Value="productForm.Name" class="form-control" 
                                   placeholder="Enter product name (something that might actually sell)" />
                        <ValidationMessage For="@(() => productForm.Name)" />
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Description</label>
                        <InputTextArea @bind-Value="productForm.Description" class="form-control" 
                                       placeholder="Describe this magnificent creation" rows="3" />
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label class="form-label">SKU *</label>
                            <InputText @bind-Value="productForm.SKU" class="form-control" 
                                       placeholder="Unique identifier" />
                            <ValidationMessage For="@(() => productForm.SKU)" />
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Category</label>
                            <InputSelect @bind-Value="productForm.Category" class="form-control">
                                @foreach (var category in Enum.GetValues<ProductCategory>())
                                {
                                    <option value="@category">@category</option>
                                }
                            </InputSelect>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label class="form-label">Price *</label>
                            <InputNumber @bind-Value="productForm.Price" class="form-control" 
                                         placeholder="0.00" step="0.01" />
                            <ValidationMessage For="@(() => productForm.Price)" />
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Cost *</label>
                            <InputNumber @bind-Value="productForm.Cost" class="form-control" 
                                         placeholder="0.00" step="0.01" />
                            <ValidationMessage For="@(() => productForm.Cost)" />
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label class="form-label">Stock Quantity</label>
                            <InputNumber @bind-Value="productForm.StockQuantity" class="form-control" />
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Reorder Level</label>
                            <InputNumber @bind-Value="productForm.ReorderLevel" class="form-control" />
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label class="form-label">Supplier</label>
                            <InputText @bind-Value="productForm.Supplier" class="form-control" 
                                       placeholder="Who do we blame when this breaks?" />
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Brand</label>
                            <InputText @bind-Value="productForm.Brand" class="form-control" 
                                       placeholder="Brand name" />
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label class="form-label">Weight</label>
                            <InputNumber @bind-Value="productForm.Weight" class="form-control" 
                                         step="0.01" />
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Weight Unit</label>
                            <InputText @bind-Value="productForm.WeightUnit" class="form-control" 
                                       placeholder="kg, lbs, etc." />
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Status</label>
                        <InputSelect @bind-Value="productForm.Status" class="form-control">
                            @foreach (var status in Enum.GetValues<ProductStatus>())
                            {
                                <option value="@status">@status</option>
                            }
                        </InputSelect>
                    </div>
                    
                    <div style="display: flex; justify-content: flex-end; gap: 1rem; margin-top: 2rem;">
                        <button type="button" class="btn btn-secondary" @onclick="HideModal">
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-primary" disabled="@isSaving">
                            @if (isSaving)
                            {
                                <text>Saving...</text>
                            }
                            else
                            {
                                <text>@(isEditMode ? "Update" : "Create") Product</text>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
}
