@using Microsoft.AspNetCore.Components.Authorization
@inject AuthenticationStateProvider AuthenticationStateProvider

<div class="top-bar">
    <div>
        <h4 style="margin: 0; color: var(--dark-text-primary);">@PageTitle</h4>
        <small style="color: var(--dark-text-muted);">@PageSubtitle</small>
    </div>
    
    <div style="display: flex; align-items: center; gap: 1rem;">
        <AuthorizeView>
            <Authorized>
                <div style="color: var(--dark-text-secondary); font-size: 0.9rem;">
                    Welcome back, @GetUserDisplayName(context.User)
                </div>
                <div style="width: 32px; height: 32px; background-color: var(--dark-accent); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
                    @GetUserInitials(context.User)
                </div>
            </Authorized>
            <NotAuthorized>
                <a href="/Account/Login" class="btn btn-primary">Login</a>
            </NotAuthorized>
        </AuthorizeView>
    </div>
</div>

@code {
    [Parameter] public string PageTitle { get; set; } = "Dashboard";
    [Parameter] public string PageSubtitle { get; set; } = "Welcome to the darkness";

    private string GetUserDisplayName(System.Security.Claims.ClaimsPrincipal user)
    {
        var name = user.Identity?.Name;
        if (string.IsNullOrEmpty(name))
            return "Anonymous Soul";
            
        // Extract name from email if it's an email
        if (name.Contains("@"))
        {
            return name.Split('@')[0];
        }
        
        return name;
    }

    private string GetUserInitials(System.Security.Claims.ClaimsPrincipal user)
    {
        var name = GetUserDisplayName(user);
        if (string.IsNullOrEmpty(name) || name == "Anonymous Soul")
            return "?";
            
        var parts = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length >= 2)
            return $"{parts[0][0]}{parts[1][0]}".ToUpper();
        else if (parts.Length == 1 && parts[0].Length >= 2)
            return parts[0].Substring(0, 2).ToUpper();
        else
            return parts[0][0].ToString().ToUpper();
    }
}
