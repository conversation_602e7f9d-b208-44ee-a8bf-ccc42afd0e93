/* DarkERP Dark Theme - Because light themes are for optimists */

:root {
    --dark-bg-primary: #1a1a1a;
    --dark-bg-secondary: #2d2d2d;
    --dark-bg-tertiary: #3a3a3a;
    --dark-text-primary: #e0e0e0;
    --dark-text-secondary: #b0b0b0;
    --dark-text-muted: #808080;
    --dark-border: #404040;
    --dark-accent: #ff6b35;
    --dark-accent-hover: #ff8c5a;
    --dark-success: #28a745;
    --dark-warning: #ffc107;
    --dark-danger: #dc3545;
    --dark-info: #17a2b8;
    --dark-shadow: rgba(0, 0, 0, 0.5);
}

/* Global dark theme */
body {
    background-color: var(--dark-bg-primary);
    color: var(--dark-text-primary);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Layout structure */
.app-container {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 250px;
    background-color: var(--dark-bg-secondary);
    border-right: 1px solid var(--dark-border);
    padding: 1rem 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
}

.main-content {
    flex: 1;
    margin-left: 250px;
    display: flex;
    flex-direction: column;
}

.top-bar {
    background-color: var(--dark-bg-secondary);
    border-bottom: 1px solid var(--dark-border);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.content-area {
    flex: 1;
    padding: 2rem;
    background-color: var(--dark-bg-primary);
}

/* Navigation styles */
.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin: 0;
}

.nav-link {
    display: block;
    padding: 0.75rem 1.5rem;
    color: var(--dark-text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background-color: var(--dark-bg-tertiary);
    color: var(--dark-text-primary);
    border-left-color: var(--dark-accent);
}

.nav-link.active {
    background-color: var(--dark-bg-tertiary);
    color: var(--dark-accent);
    border-left-color: var(--dark-accent);
}

/* Card styles */
.card {
    background-color: var(--dark-bg-secondary);
    border: 1px solid var(--dark-border);
    border-radius: 8px;
    box-shadow: 0 2px 4px var(--dark-shadow);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: var(--dark-bg-tertiary);
    border-bottom: 1px solid var(--dark-border);
    padding: 1rem 1.5rem;
    font-weight: 600;
    color: var(--dark-text-primary);
}

.card-body {
    padding: 1.5rem;
}

/* Button styles */
.btn {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--dark-accent);
    color: white;
}

.btn-primary:hover {
    background-color: var(--dark-accent-hover);
}

.btn-secondary {
    background-color: var(--dark-bg-tertiary);
    color: var(--dark-text-primary);
    border: 1px solid var(--dark-border);
}

.btn-secondary:hover {
    background-color: var(--dark-border);
}

.btn-success {
    background-color: var(--dark-success);
    color: white;
}

.btn-warning {
    background-color: var(--dark-warning);
    color: #212529;
}

.btn-danger {
    background-color: var(--dark-danger);
    color: white;
}

/* Form styles */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--dark-text-primary);
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 0.5rem 0.75rem;
    background-color: var(--dark-bg-tertiary);
    border: 1px solid var(--dark-border);
    border-radius: 4px;
    color: var(--dark-text-primary);
    font-size: 1rem;
}

.form-control:focus {
    outline: none;
    border-color: var(--dark-accent);
    box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.2);
}

.form-control::placeholder {
    color: var(--dark-text-muted);
}

/* Table styles */
.table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--dark-bg-secondary);
    border-radius: 8px;
    overflow: hidden;
}

.table th,
.table td {
    padding: 0.75rem;
    border-bottom: 1px solid var(--dark-border);
    text-align: left;
}

.table th {
    background-color: var(--dark-bg-tertiary);
    font-weight: 600;
    color: var(--dark-text-primary);
}

.table td {
    color: var(--dark-text-secondary);
}

.table tbody tr:hover {
    background-color: var(--dark-bg-tertiary);
}

/* Dashboard widgets */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.widget {
    background-color: var(--dark-bg-secondary);
    border: 1px solid var(--dark-border);
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px var(--dark-shadow);
}

.widget-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-text-primary);
}

.widget-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-accent);
}

.widget-subtitle {
    color: var(--dark-text-muted);
    font-size: 0.9rem;
}

/* Status badges */
.badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.badge-success {
    background-color: var(--dark-success);
    color: white;
}

.badge-warning {
    background-color: var(--dark-warning);
    color: #212529;
}

.badge-danger {
    background-color: var(--dark-danger);
    color: white;
}

.badge-info {
    background-color: var(--dark-info);
    color: white;
}

/* Responsive design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

/* Loading and error states */
.loading {
    text-align: center;
    padding: 2rem;
    color: var(--dark-text-muted);
}

.error {
    background-color: rgba(220, 53, 69, 0.1);
    border: 1px solid var(--dark-danger);
    border-radius: 4px;
    padding: 1rem;
    color: var(--dark-danger);
    margin-bottom: 1rem;
}

/* Blazor error UI */
#blazor-error-ui {
    background: var(--dark-danger);
    bottom: 0;
    box-shadow: 0 -1px 2px var(--dark-shadow);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
    color: white;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}
