using AutoMapper;
using DarkERP.Application.Common.Interfaces;
using DarkERP.Application.DTOs;
using DarkERP.Domain.Entities;
using DarkERP.Domain.Exceptions;

namespace DarkERP.Application.Services;

/// <summary>
/// Product service - where product dreams come to life (or die)
/// </summary>
public interface IProductService : IService<Product>
{
    Task<ProductDto?> GetProductDtoAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<ProductListDto>> GetProductListAsync(CancellationToken cancellationToken = default);
    Task<(IEnumerable<ProductListDto> Items, int TotalCount)> GetProductPagedAsync(int pageNumber, int pageSize, CancellationToken cancellationToken = default);
    Task<ProductDto> CreateProductAsync(CreateProductDto createDto, CancellationToken cancellationToken = default);
    Task<ProductDto> UpdateProductAsync(UpdateProductDto updateDto, CancellationToken cancellationToken = default);
    Task<IEnumerable<ProductListDto>> GetLowStockProductsAsync(CancellationToken cancellationToken = default);
    Task UpdateStockAsync(Guid productId, int quantity, string reason = "Manual adjustment", CancellationToken cancellationToken = default);
}

public class ProductService : IProductService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;

    public ProductService(IUnitOfWork unitOfWork, IMapper mapper)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    }

    public async Task<Product?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _unitOfWork.Products.GetByIdAsync(id, cancellationToken);
    }

    public async Task<IEnumerable<Product>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _unitOfWork.Products.GetAllAsync(cancellationToken);
    }

    public async Task<(IEnumerable<Product> Items, int TotalCount)> GetPagedAsync(int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        return await _unitOfWork.Products.GetPagedAsync(pageNumber, pageSize, cancellationToken: cancellationToken);
    }

    public async Task<ProductDto?> GetProductDtoAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var product = await _unitOfWork.Products.GetByIdAsync(id, cancellationToken);
        if (product == null) return null;

        var dto = _mapper.Map<ProductDto>(product);
        dto.IsLowStock = product.IsLowStock();
        dto.ProfitMargin = product.GetProfitMargin();
        
        return dto;
    }

    public async Task<IEnumerable<ProductListDto>> GetProductListAsync(CancellationToken cancellationToken = default)
    {
        var products = await _unitOfWork.Products.GetAllAsync(cancellationToken);
        return products.Select(p => 
        {
            var dto = _mapper.Map<ProductListDto>(p);
            dto.IsLowStock = p.IsLowStock();
            return dto;
        });
    }

    public async Task<(IEnumerable<ProductListDto> Items, int TotalCount)> GetProductPagedAsync(int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        var (products, totalCount) = await _unitOfWork.Products.GetPagedAsync(pageNumber, pageSize, cancellationToken: cancellationToken);
        var dtos = products.Select(p => 
        {
            var dto = _mapper.Map<ProductListDto>(p);
            dto.IsLowStock = p.IsLowStock();
            return dto;
        });
        
        return (dtos, totalCount);
    }

    public async Task<Product> CreateAsync(Product entity, CancellationToken cancellationToken = default)
    {
        // Check for duplicate SKU - because uniqueness matters
        var existingProduct = await _unitOfWork.Products.FirstOrDefaultAsync(p => p.SKU == entity.SKU, cancellationToken);
        if (existingProduct != null)
            throw new BusinessRuleViolationException("Duplicate SKU", $"Product with SKU '{entity.SKU}' already exists");

        var product = await _unitOfWork.Products.AddAsync(entity, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return product;
    }

    public async Task<ProductDto> CreateProductAsync(CreateProductDto createDto, CancellationToken cancellationToken = default)
    {
        var product = _mapper.Map<Product>(createDto);
        var createdProduct = await CreateAsync(product, cancellationToken);
        
        var dto = _mapper.Map<ProductDto>(createdProduct);
        dto.IsLowStock = createdProduct.IsLowStock();
        dto.ProfitMargin = createdProduct.GetProfitMargin();
        
        return dto;
    }

    public async Task<Product> UpdateAsync(Product entity, CancellationToken cancellationToken = default)
    {
        entity.UpdatedAt = DateTime.UtcNow;
        await _unitOfWork.Products.UpdateAsync(entity, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return entity;
    }

    public async Task<ProductDto> UpdateProductAsync(UpdateProductDto updateDto, CancellationToken cancellationToken = default)
    {
        var existingProduct = await _unitOfWork.Products.GetByIdAsync(updateDto.Id, cancellationToken);
        if (existingProduct == null)
            throw new ArgumentException($"Product with ID {updateDto.Id} not found");

        // Check for duplicate SKU (excluding current product)
        var duplicateSku = await _unitOfWork.Products.FirstOrDefaultAsync(
            p => p.SKU == updateDto.SKU && p.Id != updateDto.Id, cancellationToken);
        if (duplicateSku != null)
            throw new BusinessRuleViolationException("Duplicate SKU", $"Another product with SKU '{updateDto.SKU}' already exists");

        _mapper.Map(updateDto, existingProduct);
        var updatedProduct = await UpdateAsync(existingProduct, cancellationToken);
        
        var dto = _mapper.Map<ProductDto>(updatedProduct);
        dto.IsLowStock = updatedProduct.IsLowStock();
        dto.ProfitMargin = updatedProduct.GetProfitMargin();
        
        return dto;
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        await _unitOfWork.Products.SoftDeleteAsync(id, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task<IEnumerable<ProductListDto>> GetLowStockProductsAsync(CancellationToken cancellationToken = default)
    {
        var products = await _unitOfWork.Products.FindAsync(p => p.StockQuantity <= p.ReorderLevel, cancellationToken);
        return products.Select(p => 
        {
            var dto = _mapper.Map<ProductListDto>(p);
            dto.IsLowStock = true; // Obviously, since we filtered for it
            return dto;
        });
    }

    public async Task UpdateStockAsync(Guid productId, int quantity, string reason = "Manual adjustment", CancellationToken cancellationToken = default)
    {
        var product = await _unitOfWork.Products.GetByIdAsync(productId, cancellationToken);
        if (product == null)
            throw new ArgumentException($"Product with ID {productId} not found");

        if (product.StockQuantity + quantity < 0)
            throw new InsufficientInventoryException(product.Name, Math.Abs(quantity), product.StockQuantity);

        product.UpdateStock(quantity, reason);
        await _unitOfWork.Products.UpdateAsync(product, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
    }
}
