using DarkERP.Domain.Enums;

namespace DarkERP.Application.DTOs;

/// <summary>
/// Product DTO - the sanitized version for public consumption
/// </summary>
public class ProductDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string SKU { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public decimal Cost { get; set; }
    public int StockQuantity { get; set; }
    public int ReorderLevel { get; set; }
    public ProductCategory Category { get; set; }
    public ProductStatus Status { get; set; }
    public string? Supplier { get; set; }
    public string? Brand { get; set; }
    public decimal Weight { get; set; }
    public string? WeightUnit { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    
    // Computed properties
    public bool IsLowStock { get; set; }
    public decimal ProfitMargin { get; set; }
}

/// <summary>
/// Create Product DTO - what we need to birth a new product
/// </summary>
public class CreateProductDto
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string SKU { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public decimal Cost { get; set; }
    public int StockQuantity { get; set; }
    public int ReorderLevel { get; set; }
    public ProductCategory Category { get; set; }
    public ProductStatus Status { get; set; } = ProductStatus.Active;
    public string? Supplier { get; set; }
    public string? Brand { get; set; }
    public decimal Weight { get; set; }
    public string? WeightUnit { get; set; } = "kg";
}

/// <summary>
/// Update Product DTO - for when products need a makeover
/// </summary>
public class UpdateProductDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string SKU { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public decimal Cost { get; set; }
    public int StockQuantity { get; set; }
    public int ReorderLevel { get; set; }
    public ProductCategory Category { get; set; }
    public ProductStatus Status { get; set; }
    public string? Supplier { get; set; }
    public string? Brand { get; set; }
    public decimal Weight { get; set; }
    public string? WeightUnit { get; set; }
}

/// <summary>
/// Product list DTO - for when you just need the basics
/// </summary>
public class ProductListDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string SKU { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public int StockQuantity { get; set; }
    public ProductStatus Status { get; set; }
    public bool IsLowStock { get; set; }
}
