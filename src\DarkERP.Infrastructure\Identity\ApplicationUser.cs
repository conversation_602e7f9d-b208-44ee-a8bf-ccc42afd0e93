using Microsoft.AspNetCore.Identity;

namespace DarkERP.Infrastructure.Data;

/// <summary>
/// Application User - extending Identity to fit our dark purposes
/// </summary>
public class ApplicationUser : IdentityUser
{
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? LastLoginAt { get; set; }
    public bool IsActive { get; set; } = true;
    
    // Link to Employee if this user is an employee
    public Guid? EmployeeId { get; set; }
    
    public string FullName => $"{FirstName} {LastName}".Trim();
}
