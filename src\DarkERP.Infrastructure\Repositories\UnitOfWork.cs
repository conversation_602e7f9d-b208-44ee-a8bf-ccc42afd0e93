using DarkERP.Application.Common.Interfaces;
using DarkERP.Domain.Entities;
using DarkERP.Infrastructure.Data;
using Microsoft.EntityFrameworkCore.Storage;

namespace DarkERP.Infrastructure.Repositories;

/// <summary>
/// Unit of Work implementation - keeping transactions together like a dysfunctional family
/// </summary>
public class UnitOfWork : IUnitOfWork
{
    private readonly AppDbContext _context;
    private IDbContextTransaction? _transaction;
    
    // Lazy-loaded repositories - because we're efficient like that
    private IRepository<Product>? _products;
    private IRepository<Customer>? _customers;
    private IRepository<Employee>? _employees;
    private IRepository<SalesOrder>? _salesOrders;
    private IRepository<SalesOrderItem>? _salesOrderItems;
    private IRepository<PurchaseOrder>? _purchaseOrders;
    private IRepository<PurchaseOrderItem>? _purchaseOrderItems;
    private IRepository<Invoice>? _invoices;
    private IRepository<InvoiceItem>? _invoiceItems;

    public UnitOfWork(AppDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    // Repository properties - lazy initialization because we're not wasteful
    public IRepository<Product> Products => _products ??= new Repository<Product>(_context);
    public IRepository<Customer> Customers => _customers ??= new Repository<Customer>(_context);
    public IRepository<Employee> Employees => _employees ??= new Repository<Employee>(_context);
    public IRepository<SalesOrder> SalesOrders => _salesOrders ??= new Repository<SalesOrder>(_context);
    public IRepository<SalesOrderItem> SalesOrderItems => _salesOrderItems ??= new Repository<SalesOrderItem>(_context);
    public IRepository<PurchaseOrder> PurchaseOrders => _purchaseOrders ??= new Repository<PurchaseOrder>(_context);
    public IRepository<PurchaseOrderItem> PurchaseOrderItems => _purchaseOrderItems ??= new Repository<PurchaseOrderItem>(_context);
    public IRepository<Invoice> Invoices => _invoices ??= new Repository<Invoice>(_context);
    public IRepository<InvoiceItem> InvoiceItems => _invoiceItems ??= new Repository<InvoiceItem>(_context);

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            // Log the exception here if you have logging
            // For now, just rethrow with a more descriptive message
            throw new InvalidOperationException("Failed to save changes to the database. Check inner exception for details.", ex);
        }
    }

    public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
            throw new InvalidOperationException("A transaction is already in progress. Don't be greedy.");

        _transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
    }

    public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction == null)
            throw new InvalidOperationException("No transaction in progress. You can't commit what doesn't exist.");

        try
        {
            await _transaction.CommitAsync(cancellationToken);
        }
        catch
        {
            await RollbackTransactionAsync(cancellationToken);
            throw;
        }
        finally
        {
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction == null)
            throw new InvalidOperationException("No transaction in progress. You can't rollback what doesn't exist.");

        try
        {
            await _transaction.RollbackAsync(cancellationToken);
        }
        finally
        {
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public void Dispose()
    {
        _transaction?.Dispose();
        _context.Dispose();
    }
}
